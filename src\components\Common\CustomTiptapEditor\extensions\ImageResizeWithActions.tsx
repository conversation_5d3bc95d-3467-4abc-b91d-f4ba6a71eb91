import Image from "@tiptap/extension-image";
import { setConfirmModalConfig } from "stores/util";

interface ImageResizeWithActionsOptions {
  onDelete?: (imageSrc: string, assetId?: string) => void;
}

export const ImageResizeWithActions =
  Image.extend<ImageResizeWithActionsOptions>({
    addOptions() {
      return {
        ...this.parent?.(),
        onDelete: undefined,
      };
    },
    addAttributes() {
      return {
        ...this.parent?.(),
        style: {
          default: "height: auto; cursor: pointer;",
          parseHTML: (element) => {
            const width = element.getAttribute("width");
            return width
              ? `width: ${width}px; height: auto; cursor: pointer;`
              : `${element.style.cssText}`;
          },
        },
      };
    },
    addNodeView() {
      return ({ node, editor, getPos }) => {
        const {
          view,
          options: { editable },
        } = editor;
        const { style } = node.attrs;
        const $wrapper = document.createElement("div");
        const $container = document.createElement("div");
        const $img = document.createElement("img");
        const iconStyle = "width: 24px; height: 24px; cursor: pointer;";

        const dispatchNodeView = () => {
          if (typeof getPos === "function") {
            const newAttrs = {
              ...node.attrs,
              style: `${$img.style.cssText}`,
            };
            view.dispatch(
              view.state.tr.setNodeMarkup(getPos(), null, newAttrs)
            );
          }
        };
        const paintPositionContoller = () => {
          const $postionController = document.createElement("div");

          const $leftController = document.createElement("img");
          const $centerController = document.createElement("img");
          const $rightController = document.createElement("img");
          const $deleteButton = document.createElement("img");

          const controllerMouseOver = (e: any) => {
            e.target.style.opacity = 0.3;
          };

          const controllerMouseOut = (e: any) => {
            e.target.style.opacity = 1;
          };

          $postionController.setAttribute(
            "style",
            "position: absolute; top: 0%; left: 50%; width: 130px; height: 25px; z-index: 999; background-color: #fff; border-radius: 4px; cursor: pointer; transform: translate(-50%, -50%); display: flex; justify-content: space-between; align-items: center; padding: 0 10px;"
          );

          $leftController.setAttribute(
            "src",
            "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_left/default/20px.svg"
          );
          $leftController.setAttribute("style", iconStyle);
          $leftController.addEventListener("mouseover", controllerMouseOver);
          $leftController.addEventListener("mouseout", controllerMouseOut);

          $centerController.setAttribute(
            "src",
            "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_center/default/20px.svg"
          );
          $centerController.setAttribute("style", iconStyle);
          $centerController.addEventListener("mouseover", controllerMouseOver);
          $centerController.addEventListener("mouseout", controllerMouseOut);

          $rightController.setAttribute(
            "src",
            "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/format_align_right/default/20px.svg"
          );
          $rightController.setAttribute("style", iconStyle);
          $rightController.addEventListener("mouseover", controllerMouseOver);
          $rightController.addEventListener("mouseout", controllerMouseOut);

          // Configure delete button
          $deleteButton.setAttribute(
            "src",
            "https://fonts.gstatic.com/s/i/short-term/release/materialsymbolsoutlined/delete/default/20px.svg"
          );
          $deleteButton.setAttribute("style", iconStyle);
          $deleteButton.addEventListener("mouseover", controllerMouseOver);
          $deleteButton.addEventListener("mouseout", controllerMouseOut);

          $leftController.addEventListener("click", () => {
            $img.setAttribute(
              "style",
              `${$img.style.cssText} margin: 0 auto 0 0;`
            );
            dispatchNodeView();
          });
          $centerController.addEventListener("click", () => {
            $img.setAttribute("style", `${$img.style.cssText} margin: 0 auto;`);
            dispatchNodeView();
          });
          $rightController.addEventListener("click", () => {
            $img.setAttribute(
              "style",
              `${$img.style.cssText} margin: 0 0 0 auto;`
            );
            dispatchNodeView();
          });

          // Add delete button click handler
          $deleteButton.addEventListener("click", () => {
            const onDelete = this.options.onDelete;

            if (onDelete && typeof onDelete === "function") {
              // Extract image src for potential asset ID identification
              const imageSrc = node.attrs.src || "";
              const assetId = imageSrc.split("/").pop()?.split("?")[0]; // Extract potential asset ID from URL

              setConfirmModalConfig({
                visible: true,
                data: {
                  onSubmit: () => {
                    // Call the configured onDelete function
                    onDelete(imageSrc, assetId);
                    // Remove the image node from the editor
                    if (typeof getPos === "function") {
                      const pos = getPos();
                      view.dispatch(view.state.tr.delete(pos, pos + 1));
                    }
                  },
                  onClose: () => {
                    // Do nothing on close - preserves the image
                  },
                  content: {
                    heading: "Delete Image",
                    description: "Are you sure you want to delete this image?",
                  },
                  buttonText: "Delete",
                },
              });
            }
          });

          $postionController.appendChild($leftController);
          $postionController.appendChild($centerController);
          $postionController.appendChild($rightController);
          $postionController.appendChild($deleteButton);

          // Only add delete button if onDelete is configured
          const { onDelete } =
            editor.options.extensions.find((ext: any) => ext.name === "image")
              ?.options || {};

          if (onDelete && typeof onDelete === "function") {
            $postionController.appendChild($deleteButton);
          }

          $container.appendChild($postionController);
        };

        $wrapper.setAttribute("style", `display: flex;`);
        $wrapper.appendChild($container);

        $container.setAttribute("style", `${style}`);
        $container.appendChild($img);

        Object.entries(node.attrs).forEach(([key, value]) => {
          if (value === undefined || value === null) return;
          $img.setAttribute(key, value);
        });

        if (!editable) return { dom: $container };
        const isMobile = document.documentElement.clientWidth < 768;
        const dotPosition = isMobile ? "-8px" : "-4px";
        const dotsPosition = [
          `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,
          `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,
          `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,
          `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,
        ];

        let isResizing = false;
        let startX: number, startWidth: number;

        $container.addEventListener("click", () => {
          //remove remaining dots and position controller
          const isMobile = document.documentElement.clientWidth < 768;
          isMobile &&
            (
              document.querySelector(".ProseMirror-focused") as HTMLElement
            )?.blur();

          if ($container.childElementCount > 3) {
            // Remove all added elements (4 dots + 1 position controller with 3-4 buttons)
            const elementsToRemove = $container.childElementCount - 1; // Keep only the image
            for (let i = 0; i < elementsToRemove; i++) {
              if ($container.lastChild && $container.lastChild !== $img) {
                $container.removeChild($container.lastChild as Node);
              }
            }
          }

          paintPositionContoller();

          $container.setAttribute(
            "style",
            `position: relative; border: 1px dashed #6C6C6C; ${style} cursor: pointer;`
          );

          Array.from({ length: 4 }, (_, index) => {
            const $dot = document.createElement("div");
            $dot.setAttribute(
              "style",
              `position: absolute; width: ${isMobile ? 16 : 9}px; height: ${isMobile ? 16 : 9}px; border: 1.5px solid #6C6C6C; border-radius: 50%; ${dotsPosition[index]}`
            );

            $dot.addEventListener("mousedown", (e: any) => {
              e.preventDefault();
              isResizing = true;
              startX = e.clientX;
              startWidth = $container.offsetWidth;

              const onMouseMove = (e: MouseEvent) => {
                if (!isResizing) return;
                const deltaX =
                  index % 2 === 0 ? -(e.clientX - startX) : e.clientX - startX;

                const newWidth = startWidth + deltaX;

                $container.style.width = newWidth + "px";

                $img.style.width = newWidth + "px";
              };

              const onMouseUp = () => {
                if (isResizing) {
                  isResizing = false;
                }
                dispatchNodeView();

                document.removeEventListener("mousemove", onMouseMove);
                document.removeEventListener("mouseup", onMouseUp);
              };

              document.addEventListener("mousemove", onMouseMove);
              document.addEventListener("mouseup", onMouseUp);
            });

            $dot.addEventListener(
              "touchstart",
              (e: any) => {
                e.cancelable && e.preventDefault();
                isResizing = true;
                startX = e.touches[0].clientX;
                startWidth = $container.offsetWidth;

                const onTouchMove = (e: TouchEvent) => {
                  if (!isResizing) return;
                  const deltaX =
                    index % 2 === 0
                      ? -(e.touches[0].clientX - startX)
                      : e.touches[0].clientX - startX;

                  const newWidth = startWidth + deltaX;

                  $container.style.width = newWidth + "px";

                  $img.style.width = newWidth + "px";
                };

                const onTouchEnd = () => {
                  if (isResizing) {
                    isResizing = false;
                  }
                  dispatchNodeView();

                  document.removeEventListener("touchmove", onTouchMove);
                  document.removeEventListener("touchend", onTouchEnd);
                };

                document.addEventListener("touchmove", onTouchMove);
                document.addEventListener("touchend", onTouchEnd);
              },
              { passive: false }
            );
            $container.appendChild($dot);
          });
        });

        document.addEventListener("click", (e: MouseEvent) => {
          const $target = e.target as HTMLElement;
          const isClickInside =
            $container.contains($target) || $target.style.cssText === iconStyle;

          if (!isClickInside) {
            const containerStyle = $container.getAttribute("style");
            const newStyle = containerStyle?.replace(
              "border: 1px dashed #6C6C6C;",
              ""
            );
            $container.setAttribute("style", newStyle as string);

            if ($container.childElementCount > 3) {
              // Remove all added elements (4 dots + 1 position controller with 3-4 buttons)
              const elementsToRemove = $container.childElementCount - 1; // Keep only the image
              for (let i = 0; i < elementsToRemove; i++) {
                if ($container.lastChild && $container.lastChild !== $img) {
                  $container.removeChild($container.lastChild as Node);
                }
              }
            }
          }
        });

        return {
          dom: $wrapper,
        };
      };
    },
  });
